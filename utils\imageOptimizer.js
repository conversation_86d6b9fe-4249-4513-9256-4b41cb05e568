/**
 * 图片优化工具类
 * 专门针对小程序真机图片加载慢的问题进行优化
 */

class ImageOptimizer {
  constructor() {
    this.cache = new Map();
    this.preloadQueue = [];
    this.isPreloading = false;
    this.maxCacheSize = 50; // 最大缓存数量
    this.compressionQuality = 80; // 默认压缩质量
  }

  /**
   * 优化图片URL
   * @param {string} src 原始图片URL
   * @param {Object} options 优化选项
   * @returns {string} 优化后的URL
   */
  optimizeUrl(src, options = {}) {
    if (!src || src.startsWith('/assets/') || src.startsWith('data:')) {
      return src;
    }

    const {
      width = 0,
      height = 0,
      quality = this.compressionQuality,
      format = 'auto'
    } = options;

    // 针对不同CDN的优化策略
    if (src.includes('cdn.jsdelivr.net')) {
      return this.optimizeJsDelivrUrl(src, { width, height, quality });
    }
    
    if (src.includes('github.com') || src.includes('githubusercontent.com')) {
      return this.optimizeGitHubUrl(src, { width, height, quality });
    }

    // 其他CDN或自定义域名
    return this.addCompressionParams(src, { width, height, quality, format });
  }

  /**
   * 优化jsDelivr CDN URL
   */
  optimizeJsDelivrUrl(src, options) {
    const { width, height, quality } = options;
    
    // jsDelivr 本身不支持图片处理，但我们可以通过其他方式优化
    // 1. 使用更快的CDN节点
    let optimizedUrl = src.replace('cdn.jsdelivr.net', 'fastly.jsdelivr.net');
    
    // 2. 添加缓存控制参数
    const params = [];
    if (width > 0) params.push(`w=${width}`);
    if (height > 0) params.push(`h=${height}`);
    if (quality < 100) params.push(`q=${quality}`);
    
    if (params.length > 0) {
      optimizedUrl += (optimizedUrl.includes('?') ? '&' : '?') + params.join('&');
    }
    
    return optimizedUrl;
  }

  /**
   * 优化GitHub URL
   */
  optimizeGitHubUrl(src, options) {
    // GitHub 图片可以通过第三方服务进行压缩
    const { width, height, quality } = options;
    
    if (width > 0 || height > 0 || quality < 100) {
      // 使用 images.weserv.nl 服务进行图片处理
      const params = [];
      if (width > 0) params.push(`w=${width}`);
      if (height > 0) params.push(`h=${height}`);
      if (quality < 100) params.push(`q=${quality}`);
      
      return `https://images.weserv.nl/?url=${encodeURIComponent(src)}&${params.join('&')}`;
    }
    
    return src;
  }

  /**
   * 添加通用压缩参数
   */
  addCompressionParams(src, options) {
    const { width, height, quality, format } = options;
    const params = [];
    
    if (width > 0) params.push(`width=${width}`);
    if (height > 0) params.push(`height=${height}`);
    if (quality < 100) params.push(`quality=${quality}`);
    if (format !== 'auto') params.push(`format=${format}`);
    
    if (params.length > 0) {
      return src + (src.includes('?') ? '&' : '?') + params.join('&');
    }
    
    return src;
  }

  /**
   * 预加载图片
   * @param {string|Array} urls 图片URL或URL数组
   * @param {Object} options 选项
   */
  preload(urls, options = {}) {
    const urlArray = Array.isArray(urls) ? urls : [urls];
    const { priority = 'normal' } = options;
    
    urlArray.forEach(url => {
      if (url && !this.cache.has(url)) {
        this.preloadQueue.push({ url, priority, options });
      }
    });
    
    if (!this.isPreloading) {
      this.processPreloadQueue();
    }
  }

  /**
   * 处理预加载队列
   */
  async processPreloadQueue() {
    if (this.preloadQueue.length === 0) {
      this.isPreloading = false;
      return;
    }
    
    this.isPreloading = true;
    
    // 按优先级排序
    this.preloadQueue.sort((a, b) => {
      const priorityOrder = { high: 3, normal: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
    
    // 并发预加载（最多3个）
    const concurrency = 3;
    const promises = [];
    
    for (let i = 0; i < Math.min(concurrency, this.preloadQueue.length); i++) {
      const item = this.preloadQueue.shift();
      promises.push(this.preloadSingle(item.url, item.options));
    }
    
    await Promise.allSettled(promises);
    
    // 继续处理剩余队列
    setTimeout(() => this.processPreloadQueue(), 100);
  }

  /**
   * 预加载单个图片
   */
  preloadSingle(url, options = {}) {
    return new Promise((resolve) => {
      const optimizedUrl = this.optimizeUrl(url, options);
      
      wx.getImageInfo({
        src: optimizedUrl,
        success: (res) => {
          // 缓存图片信息
          this.cache.set(url, {
            optimizedUrl,
            width: res.width,
            height: res.height,
            path: res.path,
            timestamp: Date.now()
          });
          
          // 清理过期缓存
          this.cleanCache();
          
          console.log('✅ 图片预加载成功:', url);
          resolve(res);
        },
        fail: (error) => {
          console.warn('❌ 图片预加载失败:', url, error);
          resolve(null);
        }
      });
    });
  }

  /**
   * 获取缓存的图片信息
   */
  getCached(url) {
    const cached = this.cache.get(url);
    if (cached) {
      // 检查缓存是否过期（24小时）
      const isExpired = Date.now() - cached.timestamp > 24 * 60 * 60 * 1000;
      if (!isExpired) {
        return cached;
      } else {
        this.cache.delete(url);
      }
    }
    return null;
  }

  /**
   * 清理过期缓存
   */
  cleanCache() {
    if (this.cache.size <= this.maxCacheSize) {
      return;
    }
    
    // 按时间戳排序，删除最旧的缓存
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    const deleteCount = this.cache.size - this.maxCacheSize;
    for (let i = 0; i < deleteCount; i++) {
      this.cache.delete(entries[i][0]);
    }
  }

  /**
   * 批量预加载页面图片
   * @param {Array} imageList 图片列表
   * @param {Object} options 选项
   */
  preloadPageImages(imageList, options = {}) {
    const { 
      maxCount = 10, 
      imageType = 'dish',
      priority = 'normal' 
    } = options;
    
    // 根据图片类型设置默认尺寸
    const sizeMap = {
      dish: { width: 400, height: 300 },
      avatar: { width: 240, height: 240 },
      menu: { width: 600, height: 400 },
      thumbnail: { width: 160, height: 160 }
    };
    
    const defaultSize = sizeMap[imageType] || { width: 400, height: 300 };
    
    // 只预加载前N张图片
    const urlsToPreload = imageList
      .slice(0, maxCount)
      .map(item => typeof item === 'string' ? item : item.image || item.img || item.src)
      .filter(Boolean);
    
    this.preload(urlsToPreload, { 
      priority, 
      ...defaultSize,
      quality: 75 // 预加载使用较低质量
    });
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.cache.clear();
    this.preloadQueue = [];
  }
}

// 创建全局实例
const imageOptimizer = new ImageOptimizer();

module.exports = {
  ImageOptimizer,
  imageOptimizer
};
