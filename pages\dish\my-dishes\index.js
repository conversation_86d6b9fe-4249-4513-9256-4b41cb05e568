const {dishApi} = require('../../../services/api');
const {baseURL} = require('../../../config/env');
const {
  calculateScrollHeight,
  presets
} = require('../../../utils/heightCalculator');
const ApiTest = require('../../../utils/apiTest');
import Dialog from '@vant/weapp/dialog/dialog';

// 标签映射
const TAG_LABELS = {
  spicy: '辣',
  numbing: '麻',
  sweet: '甜',
  sour: '酸',
  salty: '咸',
  light: '清淡',
  rich: '浓郁',
  vegetarian: '素食'
};

Page({
  data: {
    dishes: [],
    currentFilter: 'all', // all, published, unpublished
    publishedCount: 0,
    unpublishedCount: 0,
    scrollViewHeight: 400, // 滚动区域高度

    // refresh-scroll组件配置
    requestUrl: '',
    requestParams: {},
    requestHeaders: {},

    // 空状态文本
    emptyTitle: '暂无菜品',
    emptyDesc: '开始创建你的第一道菜品吧'
  },

  // 防抖定时器
  _setupTimer: null,

  onLoad() {
    // 初始化空状态文本
    const emptyTexts = this.getEmptyTexts(this.data.currentFilter);
    this.setData({
      emptyTitle: emptyTexts.title,
      emptyDesc: emptyTexts.desc
    });

    this.calculateHeight();
    this.setupRefreshScroll();
  },

  onShow() {
    // 从其他页面返回时刷新数据
    this.setupRefreshScroll();
  },

  onReady() {
    // 页面渲染完成后重新计算高度
    this.calculateHeight();
  },

  onResize() {
    // 屏幕尺寸变化时重新计算高度（如横竖屏切换）
    setTimeout(() => {
      this.calculateHeight();
    }, 100);
  },

  onPullDownRefresh() {
    this.onRefresh();
  },

  // 计算滚动区域高度
  async calculateHeight() {
    try {
      // 使用工具函数计算高度，采用列表页面预设配置
      const result = await calculateScrollHeight({
        headerSelector: '.header-section',
        ...presets.list,
        callback: height => {
          this.setData({
            scrollViewHeight: height
          });
        }
      });

      return result;
    } catch (error) {
      console.error('高度计算失败，使用降级方案:', error);

      // 使用降级高度
      const fallbackHeight = error.height || 400;
      this.setData({
        scrollViewHeight: fallbackHeight
      });
    }
  },

  // 设置refresh-scroll组件配置（带防抖）
  setupRefreshScroll() {
    // 清除之前的定时器
    if (this._setupTimer) {
      clearTimeout(this._setupTimer);
    }

    // 防抖处理，避免频繁设置
    this._setupTimer = setTimeout(() => {
      const token =
        wx.getStorageSync('token') || wx.getStorageSync('userToken');
      // 使用统一的环境配置
      console.log('🔗 使用API地址:', baseURL);

      let requestParams = {
        size: 10
      };

      // 根据筛选条件添加参数
      if (this.data.currentFilter === 'published') {
        requestParams.isPublished = true;
      } else if (this.data.currentFilter === 'unpublished') {
        requestParams.isPublished = false;
      }

      this.setData({
        requestUrl: `${baseURL}/dishes/my`,
        requestParams: requestParams,
        requestHeaders: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      this._setupTimer = null;
    }, 100); // 100ms防抖
  },

  // 处理refresh-scroll组件的数据变化
  onDataChange(e) {
    const {list, total, hasMore, isEmpty, originalResponse} = e.detail;

    // 格式化菜品数据，确保标签是数组格式，并预处理显示数据
    const formattedDishes = list.map(dish => ({
      ...dish,
      tags: Array.isArray(dish.tags)
        ? dish.tags
        : dish.tags
        ? JSON.parse(dish.tags)
        : [],
      // 预格式化时间显示
      formattedTime: this.formatTime(dish.createdAt),
      // 预格式化标签显示
      formattedTags: (Array.isArray(dish.tags)
        ? dish.tags
        : dish.tags
        ? JSON.parse(dish.tags)
        : []
      ).map(tag => this.getTagLabel(tag))
    }));

    this.setData({
      dishes: formattedDishes,
      hasMore: hasMore
    });

    // 更新统计数据
    this.updateStats();
  },

  // 更新统计数据
  async updateStats() {
    try {
      // 并行获取已上架和未上架的数量
      const [publishedResult, unpublishedResult] = await Promise.all([
        dishApi.getMyDishes({
          page: 1,
          size: 1,
          isPublished: 'true'
        }),
        dishApi.getMyDishes({
          page: 1,
          size: 1,
          isPublished: 'false'
        })
      ]);

      this.setData({
        publishedCount: publishedResult?.data?.total || 0,
        unpublishedCount: unpublishedResult?.data?.total || 0
      });
    } catch (error) {
      console.error('更新统计数据失败:', error);
    }
  },

  // 切换筛选条件
  switchFilter(e) {
    const filter = e.currentTarget.dataset.filter;
    if (filter === this.data.currentFilter) return;

    // 更新空状态文本
    const emptyTexts = this.getEmptyTexts(filter);

    this.setData({
      currentFilter: filter,
      dishes: [],
      emptyTitle: emptyTexts.title,
      emptyDesc: emptyTexts.desc
    });

    // 重新设置refresh-scroll配置
    this.setupRefreshScroll();
  },

  // 切换上架状态
  async togglePublishStatus(e) {
    const {id, status} = e.currentTarget.dataset;
    const newStatus = !status;

    try {
      wx.showLoading({
        title: '更新中...'
      });
      const result = await dishApi.updateDishStatus(id, {
        isPublished: newStatus
      });
      // 改进的成功判断逻辑
      const isSuccess =
        (result && result.success === true) ||
        (result && result.code >= 200 && result.code < 300);
      if (isSuccess) {
        // 更新本地数据
        const dishes = this.data.dishes.map(dish => {
          if (dish.id === id) {
            return {
              ...dish,
              isPublished: newStatus
            };
          }
          return dish;
        });

        this.setData({
          dishes
        });
        // 更新统计数据
        this.updateStats();
      } else {
        throw new Error(result?.message || '更新失败');
      }
    } catch (error) {
      console.error('更新状态失败:', error);
    } finally {
      wx.hideLoading();
    }
  },

  // 编辑菜品
  editDish(e) {
    const dishId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/dish/add/index?dishId=${dishId}`
    });
  },

  // 删除菜品
  async deleteDish(e) {
    const {id, name} = e.currentTarget.dataset;

    try {
      wx.showLoading({title: '分析影响中...'});

      // 先获取影响分析
      const impactResult = await dishApi.getDishImpact(id);
      wx.hideLoading();

      if (impactResult.code === 200) {
        const impact = impactResult.data;
        await this.showDeleteConfirmation(id, name, impact);
      } else {
        // 如果获取影响分析失败，使用简单确认
        await this.showSimpleDeleteConfirmation(id, name);
      }
    } catch (error) {
      wx.hideLoading();
      console.error('获取菜品影响分析失败:', error);
      // 降级到简单确认
      await this.showSimpleDeleteConfirmation(id, name);
    }
  },

  // 显示删除确认（带影响分析）
  async showDeleteConfirmation(id, name, impact) {
    const {summary, recommendations, canDelete} = impact;

    let content = `确定要删除菜品"${name}"吗？\n\n`;

    if (summary.total > 0) {
      content += `⚠️ 影响范围：\n`;
      if (summary.menuItems > 0) {
        content += `• ${summary.menuItems} 个菜单项\n`;
      }
      if (summary.orders > 0) {
        content += `• ${summary.orders} 个历史订单\n`;
      }
      content += `\n`;

      if (recommendations.length > 0) {
        content += `💡 建议：\n`;
        recommendations.forEach(rec => {
          content += `• ${rec}\n`;
        });
      }
    } else {
      content += `✅ 此菜品未被引用，可以安全删除。`;
    }

    const modalResult = await wx.showModal({
      title: canDelete ? '确认删除' : '删除警告',
      content: content,
      confirmText: canDelete ? '删除' : '强制删除',
      confirmColor: canDelete ? '#ff4757' : '#ff6b6b',
      showCancel: true
    });

    if (modalResult.confirm) {
      await this.performDelete(id, name, !canDelete);
    }
  },

  // 显示简单删除确认
  async showSimpleDeleteConfirmation(id, name) {
    const modalResult = await Dialog.confirm({
      title: '确认删除',
      message: `确定要删除菜品"${name}"吗？删除后无法恢复。`,
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      confirmButtonColor: '#EF4444'
    });

    if (modalResult) {
      await this.performDelete(id, name, false);
    }
  },

  // 执行删除操作
  async performDelete(id, name, force = false) {
    try {
      wx.showLoading({title: '删除中...'});

      const result = await dishApi.deleteDish(id, force);

      // 改进的成功判断逻辑
      const isSuccess =
        (result && result.success === true) ||
        (result && result.code >= 200 && result.code < 300) ||
        (result && result.message && result.message.includes('成功'));

      if (isSuccess) {
        // 从列表中移除
        const dishes = this.data.dishes.filter(dish => dish.id !== id);
        this.setData({dishes});

        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });

        // 更新统计数据
        this.updateStats();

        // 如果当前页没有数据了，重新设置refresh-scroll
        if (dishes.length === 0) {
          this.setupRefreshScroll();
        }
      } else {
        throw new Error(result?.message || '删除失败');
      }
    } catch (error) {
      console.error('删除菜品失败:', error);

      // 处理特殊错误码
      if (error.message && error.message.includes('DISH_REFERENCED')) {
        wx.showModal({
          title: '删除失败',
          content: '菜品正在被使用中，请先从相关菜单中移除后再删除',
          showCancel: false
        });
      } else {
        wx.showToast({
          title: error.message || '删除失败',
          icon: 'none'
        });
      }
    } finally {
      wx.hideLoading();
    }
  },

  // 查看菜品详情
  viewDishDetail(e) {
    const dish = e.currentTarget.dataset.dish;
    // 暂时跳转到编辑页面
    this.editDish(e);
  },

  // 跳转到新增菜品
  goToAddDish() {
    wx.navigateTo({
      url: '/pages/dish/add/index'
    });
  },

  // 获取标签显示名称
  getTagLabel(tag) {
    return TAG_LABELS[tag] || tag;
  },

  // 格式化时间
  formatTime(timeStr) {
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;

    // 小于1小时显示分钟
    if (diff < 60 * 60 * 1000) {
      const minutes = Math.floor(diff / (60 * 1000));
      return minutes < 1 ? '刚刚' : `${minutes}分钟前`;
    }

    // 小于24小时显示小时
    if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000));
      return `${hours}小时前`;
    }

    // 小于7天显示天数
    if (diff < 7 * 24 * 60 * 60 * 1000) {
      const days = Math.floor(diff / (24 * 60 * 60 * 1000));
      return `${days}天前`;
    }

    // 超过7天显示具体日期
    return time.toLocaleDateString('zh-CN', {
      month: 'numeric',
      day: 'numeric'
    });
  },

  // 获取空状态文本（辅助方法）
  getEmptyTexts(filter) {
    switch (filter) {
      case 'published':
        return {
          title: '暂无已上架菜品',
          desc: ''
        };
      case 'unpublished':
        return {
          title: '暂无未上架菜品',
          desc: ''
        };
      default:
        return {
          title: '暂无菜品',
          desc: ''
        };
    }
  },

  // 获取空状态标题（保留兼容性）
  getEmptyTitle() {
    return this.getEmptyTexts(this.data.currentFilter).title;
  },

  // 获取空状态描述（保留兼容性）
  getEmptyDesc() {
    return this.getEmptyTexts(this.data.currentFilter).desc;
  }
});
