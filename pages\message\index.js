const app = getApp();
const {notificationApi} = require('../../services/api');

Page({
  data: {
    notices: [],
    noticeInput: '',
    userInfo: {},
    loading: true,
    refreshing: false
  },

  onLoad() {
    // 获取用户信息
    const userInfo =
      wx.getStorageSync('userInfo') || app.globalData?.userInfo || {};
    this.setData({userInfo});

    // 加载通知数据
    this.loadNotifications();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadNotifications();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({refreshing: true});
    this.loadNotifications().finally(() => {
      wx.stopPullDownRefresh();
      this.setData({refreshing: false});
    });
  },

  // 加载通知数据
  async loadNotifications() {
    try {
      const result = await notificationApi.getNotifications();

      if (result.code === 200) {
        // 确保 result.data 是数组
        let notificationData = result.data;

        // 处理不同的数据格式
        if (!Array.isArray(notificationData)) {
          console.warn('通知数据不是数组格式:', notificationData);
          // 尝试从不同的属性中获取数组数据
          notificationData =
            notificationData.notifications ||
            notificationData.list ||
            notificationData.data ||
            [];
        }

        // 确保最终是数组
        if (!Array.isArray(notificationData)) {
          notificationData = [];
        }

        // 格式化通知数据
        const notices = notificationData.map(notification => ({
          id: notification.id,
          content: notification.content,
          time: this.formatTime(notification.createdAt),
          userName:
            notification.user?.name || notification.sender?.name || '系统',
          read: notification.read
        }));

        this.setData({
          notices,
          loading: false
        });
      } else {
        throw new Error(result.message || '加载失败');
      }
    } catch (error) {
      this.setData({loading: false});

      // 如果是首次加载失败，显示默认数据
      if (this.data.notices.length === 0) {
        this.setData({
          notices: [
            {
              id: 'default-1',
              content: '欢迎使用楠楠家厨！',
              time: this.formatTime(new Date()),
              userName: '系统',
              read: false
            }
          ]
        });
      }

      // 使用新的 UI 工具显示错误
      if (app.ui) {
        app.ui.showError('加载通知失败，请稍后重试');
      } else {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    }
  },

  // 格式化时间
  formatTime(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 通知输入
  onNoticeInput(e) {
    this.setData({
      noticeInput: e.detail.value
    });
  },

  // 添加通知
  async addNotice() {
    const {noticeInput} = this.data;

    if (!noticeInput.trim()) {
      if (app.ui) {
        app.ui.showError('请输入通知内容');
      } else {
        wx.showToast({
          title: '请输入通知内容',
          icon: 'none'
        });
      }
      return;
    }

    try {
      // 使用新的 UI 工具显示加载
      if (app.ui) {
        await app.ui.withLoading(
          async () => {
            // 调用API创建通知
            const result = await notificationApi.createNotification({
              content: noticeInput.trim()
            });

            if (result.code === 200) {
              // 清空输入框
              this.setData({noticeInput: ''});

              // 重新加载通知列表
              await this.loadNotifications();

              return result;
            } else {
              throw new Error(result.message || '发布失败');
            }
          },
          {
            loadingText: '发布中...',
            successText: '通知已发布',
            showSuccess: true
          }
        );
      } else {
        // 降级处理
        wx.showLoading({title: '发布中...'});

        const result = await notificationApi.createNotification({
          content: noticeInput.trim()
        });

        if (result.code === 200) {
          this.setData({noticeInput: ''});
          await this.loadNotifications();
          wx.showToast({
            title: '通知已发布',
            icon: 'success'
          });
        } else {
          throw new Error(result.message || '发布失败');
        }

        wx.hideLoading();
      }
    } catch (error) {
      if (!app.ui) {
        wx.hideLoading();
        wx.showToast({
          title: error.message || '发布失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
      // 如果使用 app.ui，错误会自动显示
    }
  },

  // 跳转到家庭留言页面
  goToFamilyMessage() {
    wx.navigateTo({
      url: '/pages/family_message/index'
    });
  }
});
