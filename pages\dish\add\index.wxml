<wxs module="utils" src="./utils.wxs"></wxs>

<view class="container">
	<!-- 页面标题 -->
	<view class="card-header">
		<van-icon name="{{isEditMode ? 'edit' : 'plus'}}" size="40rpx" color="#3B82F6" />
		<text class="ml-2">{{pageTitle}}</text>
	</view>

	<!-- 表单卡片 -->
	<view class="card">
		<!-- 菜品名称 -->
		<view class="input-group">
			<view class="input-label">菜品名称 <text style="color: red;">*</text></view>
			<input class="input" placeholder="请输入菜品名称，如红烧肉" bindinput="onNameInput" value="{{name}}" />
		</view>

		<!-- 菜品图片 - 必填 -->
		<view class="input-group">
			<view class="input-label">菜品图片 <text style="color: red;">*</text></view>
			<view class="flex items-center">
				<view class="btn btn-secondary mr-3" bindtap="chooseImage">
					<van-icon name="photo-o" size="32rpx" />
					<text class="ml-1">选择图片</text>
				</view>
				<!-- <text class="text-sm text-red-500">必须上传菜品图片</text> -->
			</view>
			<view wx:if="{{tempImagePath || uploadedImageUrl}}" class="image-container">
				<safe-image src="{{uploadedImageUrl || tempImagePath}}" imageType="dish" custom-class="dish-image-container" image-class="dish-image" mode="aspectFill" previewable="{{true}}" />
				<view class="image-actions">
					<view class="action-btn" bindtap="previewImage">
						<van-icon name="eye-o" size="32rpx" color="#fff" />
					</view>
					<view class="action-btn delete-btn" bindtap="deleteImage">
						<van-icon name="delete-o" size="32rpx" color="#fff" />
					</view>
				</view>
			</view>
		</view>

		<!-- 菜品分类 -->
		<view class="input-container">
			<view class="picker-label">
				<van-icon name="apps-o" class="icon-margin" />
				菜品分类 <text style="color: red;">*</text>
			</view>
			<view class="custom-picker" bindtap="showCategoryModal">
				<text class="picker-text">{{categoryLabel}}</text>
				<van-icon name="arrow-down" class="arrow-icon" />
			</view>
		</view>

		<!-- 菜品标签 -->
		<view class="input-group">
			<view class="input-label">菜品标签</view>
			<view class="tags-container">
				<view wx:for="{{availableTags}}" wx:key="value" class="{{utils.getTagClass(selectedTags, item.value)}}" style="border-color: {{item.color}}; color: {{utils.getTagTextColor(selectedTags, item.value, item.color)}}; background-color: {{utils.getTagBgColor(selectedTags, item.value, item.color)}};" bindtap="toggleTag" data-tag="{{item.value}}">
					{{item.label}}
					<text wx:if="{{utils.showCheckMark(selectedTags, item.value)}}" class="tag-check">✓</text>
				</view>
			</view>
		</view>

		<!-- 原材料 -->
		<view class="input-container">
			<view class="picker-label">
				<van-icon name="shopping-cart-o" class="icon-margin" />
				原材料 <text style="color: red;">*</text>
			</view>
			<textarea class="input-warm textarea-style" placeholder="原材料（如五花肉、酱油、糖等）" placeholder-class="placeholder-style" bindinput="onMaterialInput" value="{{material}}" auto-height maxlength="200" show-confirm-bar="{{false}}"></textarea>
		</view>

		<!-- 制作方法 -->
		<view class="input-container">
			<view class="picker-label">
				<van-icon name="notes-o" class="icon-margin" />
				制作方法 <text style="color: red;">*</text>
			</view>
			<textarea class="input-warm textarea-style" placeholder="制作方法（如步骤说明）" placeholder-class="placeholder-style" bindinput="onMethodInput" value="{{method}}" auto-height maxlength="500" show-confirm-bar="{{false}}"></textarea>
		</view>

		<!-- 备注说明 -->
		<view class="input-container">
			<view class="picker-label">
				<van-icon name="edit" class="icon-margin" />
				备注说明
			</view>
			<textarea class="input-warm textarea-style" placeholder="备注（如口味、适合人群等，可选）" placeholder-class="placeholder-style" bindinput="onRemarkInput" value="{{remark}}" auto-height maxlength="200" show-confirm-bar="{{false}}"></textarea>
		</view>

		<!-- 是否上架 -->
		<view class="input-group">
			<view class="input-label">是否上架</view>
			<view class="switch-container">
				<switch checked="{{isPublished}}" bindchange="onPublishChange" color="#3B82F6" />
				<text class="switch-text">{{isPublished ? '上架（其他人可见）' : '下架（仅自己可见）'}}</text>
			</view>
		</view>

		<!-- 提交按钮 -->
		<button class="submit-btn {{loading ? 'loading' : ''}}" bindtap="submitForm" disabled="{{loading}}">
			<van-icon wx:if="{{loading}}" name="loading" class="icon-margin" />
			<van-icon wx:else name="{{isEditMode ? 'edit' : 'plus'}}" class="icon-margin" />
			{{loading ? (isEditMode ? '更新中...' : '提交中...') : (isEditMode ? '更新菜品' : '新增菜品')}}
		</button>
	</view>

	<!-- 分类选择弹窗 -->
	<view class="category-modal {{showCategoryPicker ? 'show' : ''}}" bindtap="hideCategoryModal">
		<view class="modal-content" catchtap="stopPropagation">
			<view class="modal-header">
				<text class="modal-title">选择菜品分类</text>
				<van-icon name="cross" class="close-icon" bindtap="hideCategoryModal" />
			</view>
			<view class="category-grid">
				<view wx:for="{{categories}}" wx:key="value" class="category-item {{category === item.value ? 'selected' : ''}}" bindtap="selectCategory" data-index="{{index}}" data-value="{{item.value}}" data-label="{{item.label}}">
					<view class="category-icon">{{item.icon}}</view>
					<text class="category-label">{{item.label}}</text>
					<view wx:if="{{category === item.value}}" class="selected-mark"></view>
				</view>
			</view>
		</view>
	</view>
</view>