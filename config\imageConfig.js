/**
 * 图片优化配置
 * 统一管理图片相关的配置参数
 */

// 图片尺寸预设
const IMAGE_SIZES = {
  // 菜品图片
  dish: {
    thumbnail: { width: 160, height: 120, quality: 70 },
    small: { width: 240, height: 180, quality: 75 },
    medium: { width: 400, height: 300, quality: 80 },
    large: { width: 600, height: 450, quality: 85 }
  },
  
  // 用户头像
  avatar: {
    small: { width: 80, height: 80, quality: 85 },
    medium: { width: 120, height: 120, quality: 85 },
    large: { width: 240, height: 240, quality: 90 }
  },
  
  // 菜单图片
  menu: {
    thumbnail: { width: 200, height: 150, quality: 70 },
    medium: { width: 400, height: 300, quality: 75 },
    large: { width: 800, height: 600, quality: 80 }
  },
  
  // 通用图片
  general: {
    small: { width: 200, height: 200, quality: 75 },
    medium: { width: 400, height: 400, quality: 80 },
    large: { width: 800, height: 800, quality: 85 }
  }
};

// CDN配置
const CDN_CONFIG = {
  // jsDelivr CDN优化
  jsdelivr: {
    // 使用更快的节点
    fastNode: 'fastly.jsdelivr.net',
    // 备用节点
    backupNodes: [
      'cdn.jsdelivr.net',
      'gcore.jsdelivr.net'
    ]
  },
  
  // 图片处理服务
  imageProxy: {
    // 使用 images.weserv.nl 进行图片处理
    weserv: 'https://images.weserv.nl/',
    // 备用服务
    backup: 'https://wsrv.nl/'
  }
};

// 缓存配置
const CACHE_CONFIG = {
  // 本地缓存
  local: {
    maxSize: 50, // 最大缓存数量
    expireTime: 24 * 60 * 60 * 1000, // 24小时过期
    cleanupThreshold: 60 // 超过60个时清理
  },
  
  // 预加载配置
  preload: {
    maxConcurrency: 3, // 最大并发数
    maxCount: 10, // 每页最大预加载数量
    priority: {
      high: 3,
      normal: 2,
      low: 1
    }
  }
};

// 网络优化配置
const NETWORK_CONFIG = {
  // 重试配置
  retry: {
    maxAttempts: 3,
    delay: 1000, // 基础延迟
    backoff: 1.5 // 退避倍数
  },
  
  // 超时配置
  timeout: {
    preload: 10000, // 预加载超时
    normal: 15000   // 正常加载超时
  }
};

// 根据网络状态调整配置
const getNetworkOptimizedConfig = () => {
  return new Promise((resolve) => {
    wx.getNetworkType({
      success: (res) => {
        const networkType = res.networkType;
        let config = {};
        
        switch (networkType) {
          case 'wifi':
            config = {
              quality: 85,
              preloadCount: 10,
              enableProgressive: true
            };
            break;
          case '4g':
            config = {
              quality: 80,
              preloadCount: 8,
              enableProgressive: true
            };
            break;
          case '3g':
            config = {
              quality: 70,
              preloadCount: 5,
              enableProgressive: false
            };
            break;
          case '2g':
            config = {
              quality: 60,
              preloadCount: 3,
              enableProgressive: false
            };
            break;
          default:
            config = {
              quality: 75,
              preloadCount: 6,
              enableProgressive: true
            };
        }
        
        resolve(config);
      },
      fail: () => {
        // 网络检测失败，使用默认配置
        resolve({
          quality: 75,
          preloadCount: 6,
          enableProgressive: true
        });
      }
    });
  });
};

// 获取图片配置
const getImageConfig = (type = 'dish', size = 'medium') => {
  const sizeConfig = IMAGE_SIZES[type] && IMAGE_SIZES[type][size];
  if (!sizeConfig) {
    console.warn(`未找到图片配置: ${type}.${size}，使用默认配置`);
    return IMAGE_SIZES.dish.medium;
  }
  return sizeConfig;
};

// 获取优化后的CDN URL
const getOptimizedCdnUrl = (originalUrl) => {
  if (!originalUrl || originalUrl.startsWith('/assets/')) {
    return originalUrl;
  }
  
  // 替换为更快的CDN节点
  if (originalUrl.includes('cdn.jsdelivr.net')) {
    return originalUrl.replace('cdn.jsdelivr.net', CDN_CONFIG.jsdelivr.fastNode);
  }
  
  return originalUrl;
};

// 检查是否需要使用图片代理
const shouldUseImageProxy = (url) => {
  // GitHub图片或其他需要代理的图片
  return url.includes('github.com') || 
         url.includes('githubusercontent.com') ||
         url.includes('raw.githubusercontent.com');
};

module.exports = {
  IMAGE_SIZES,
  CDN_CONFIG,
  CACHE_CONFIG,
  NETWORK_CONFIG,
  getNetworkOptimizedConfig,
  getImageConfig,
  getOptimizedCdnUrl,
  shouldUseImageProxy
};
