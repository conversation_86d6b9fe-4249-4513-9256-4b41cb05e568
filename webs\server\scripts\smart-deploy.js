#!/usr/bin/env node
/**
 * 智能部署脚本
 * 根据文件变更智能决定是否需要重启服务
 */

const { execSync } = require('child_process');
const fs = require('fs');

// 配置
const CONFIG = {
  // 需要监控的服务器相关路径
  serverPaths: [
    'webs/server/',
    'package.json',
    'package-lock.json'
  ],
  
  // 需要监控的前端相关路径（仅更新代码，不重启服务）
  frontendPaths: [
    'pages/',
    'components/',
    'utils/',
    'styles/',
    'assets/',
    'config/env.js',
    'app.js',
    'app.json',
    'app.scss'
  ],
  
  // 管理后台相关路径
  adminPaths: [
    'webs/admin/'
  ]
};

function log(message) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
}

function execCommand(command, description) {
  try {
    log(`执行: ${description}`);
    const result = execSync(command, { encoding: 'utf8' });
    return result.trim();
  } catch (error) {
    log(`错误: ${description} - ${error.message}`);
    throw error;
  }
}

function analyzeChanges() {
  try {
    // 获取最近两次commit的差异
    const changedFiles = execCommand(
      'git diff --name-only HEAD~1 HEAD',
      '获取文件变更列表'
    );
    
    if (!changedFiles) {
      log('没有文件变更');
      return { needServerRestart: false, needAdminBuild: false, changedFiles: [] };
    }
    
    const files = changedFiles.split('\n').filter(f => f.trim());
    log(`变更文件: ${files.join(', ')}`);
    
    let needServerRestart = false;
    let needAdminBuild = false;
    
    // 分析变更类型
    for (const file of files) {
      // 检查服务器相关变更
      if (CONFIG.serverPaths.some(path => file.startsWith(path))) {
        needServerRestart = true;
        log(`检测到服务器代码变更: ${file}`);
      }
      
      // 检查管理后台变更
      if (CONFIG.adminPaths.some(path => file.startsWith(path))) {
        needAdminBuild = true;
        log(`检测到管理后台代码变更: ${file}`);
      }
      
      // 前端变更只记录，不需要特殊处理
      if (CONFIG.frontendPaths.some(path => file.startsWith(path))) {
        log(`检测到前端代码变更: ${file}`);
      }
    }
    
    return { needServerRestart, needAdminBuild, changedFiles: files };
    
  } catch (error) {
    log('分析变更失败，默认重启服务');
    return { needServerRestart: true, needAdminBuild: false, changedFiles: [] };
  }
}

function deployServer() {
  log('🚀 开始服务器部署...');
  
  try {
    // 安装依赖
    execCommand('npm install', '安装服务器依赖');
    
    // 生成Prisma客户端
    execCommand('npx prisma generate', '生成Prisma客户端');
    
    // 同步数据库
    execCommand('npx prisma db push', '同步数据库结构');
    
    // 重启PM2服务
    try {
      execCommand('pm2 restart nannan-api-test', '重启测试环境');
    } catch (e) {
      log('测试环境重启失败，可能服务不存在');
    }
    
    try {
      execCommand('pm2 restart nannan-api', '重启生产环境');
    } catch (e) {
      log('生产环境重启失败，可能服务不存在');
    }
    
    log('✅ 服务器部署完成');
    
  } catch (error) {
    log('❌ 服务器部署失败');
    throw error;
  }
}

function deployAdmin() {
  log('🎨 开始管理后台部署...');
  
  try {
    // 这里可以添加管理后台的构建和部署逻辑
    log('管理后台部署逻辑待实现');
    log('✅ 管理后台部署完成');
    
  } catch (error) {
    log('❌ 管理后台部署失败');
    throw error;
  }
}

function main() {
  log('🔍 开始智能部署分析...');
  
  const analysis = analyzeChanges();
  
  log('📊 部署决策:');
  log(`  服务器重启: ${analysis.needServerRestart ? '是' : '否'}`);
  log(`  管理后台构建: ${analysis.needAdminBuild ? '是' : '否'}`);
  
  if (analysis.needServerRestart) {
    deployServer();
  } else {
    log('⏭️ 跳过服务器部署');
  }
  
  if (analysis.needAdminBuild) {
    deployAdmin();
  } else {
    log('⏭️ 跳过管理后台部署');
  }
  
  if (!analysis.needServerRestart && !analysis.needAdminBuild) {
    log('ℹ️ 只有前端代码变更，无需重启服务');
  }
  
  log('🎉 智能部署完成');
}

// 执行主函数
if (require.main === module) {
  main();
}
