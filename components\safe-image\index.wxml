<view class="safe-image-container custom-class" style="{{style}}">
  <!-- 加载状态 -->
  <view wx:if="{{loading && showLoading}}" class="safe-image-loading">
    <van-loading type="spinner" size="24rpx" color="#999" />
    <!-- <text class="loading-text">加载中...</text> -->
    <!-- 渐进式加载进度 -->
    <view wx:if="{{progressive && loadProgress > 0}}" class="load-progress">
      <text class="progress-text">{{loadProgress}}%</text>
    </view>
  </view>

  <!-- 图片 -->
  <image
    wx:if="{{currentSrc}}"
    class="safe-image image-class {{progressive && loadProgress < 100 ? 'progressive-loading' : ''}}"
    src="{{currentSrc}}"
    mode="{{mode}}"
    lazy-load="{{lazyLoad && !inViewport}}"
    bindload="onImageLoad"
    binderror="onImageError"
    bindtap="onImageTap"
    show-menu-by-longpress="{{previewable}}"
  />

  <!-- 错误状态（仅在默认图片也加载失败时显示） -->
  <view
    wx:if="{{error && !currentSrc}}"
    class="safe-image-error {{imageType}}-error"
  >
    <view class="error-placeholder">
      <van-icon name="{{getErrorIcon(imageType)}}" size="48rpx" color="#ccc" />
      <text class="error-text">{{getErrorText(imageType)}}</text>
    </view>
    <view class="retry-btn" bindtap="reload">
      <van-icon name="replay" size="32rpx" color="#666" />
      <text>重试</text>
    </view>
  </view>

  <!-- 重试提示 -->
  <view wx:if="{{retryCount > 0 && loading}}" class="retry-indicator">
    <text>重试中 {{retryCount}}/{{maxRetry}}</text>
  </view>

  <!-- 缓存状态指示器（仅调试时显示） -->
  <view wx:if="{{cacheKey}}" class="cache-indicator">
    <van-icon name="success" size="16rpx" color="#10b981" />
  </view>
</view>
