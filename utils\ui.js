/**
 * UI 交互工具类
 * 统一管理 Toast、Loading、Dialog 等交互提示
 */

// 在小程序环境中，Vant组件通过全局方式引入
// 这里我们使用兼容的方式处理
let Toast, Dialog, Notify;

try {
  // 尝试从Vant获取组件
  if (typeof getApp === 'function') {
    // 小程序环境，组件已通过app.json引入
    Toast = require('@vant/weapp/lib/toast/toast');
    Dialog = require('@vant/weapp/lib/dialog/dialog');
    Notify = require('@vant/weapp/lib/notify/notify');
  }
} catch (error) {
  // 如果无法加载Vant组件，使用原生微信API
}

class UIManager {
  constructor() {
    this.loadingCount = 0; // 防止多个loading同时显示
  }

  /**
   * 显示成功提示
   * @param {string} message 提示信息
   * @param {number} duration 持续时间，默认2000ms
   */
  showSuccess(message, duration = 2000) {
    if (Toast && Toast.success) {
      Toast.success({
        message,
        duration,
        forbidClick: true
      });
    } else {
      // 使用原生微信API
      wx.showToast({
        title: message,
        icon: 'success',
        duration
      });
    }
  }

  /**
   * 显示失败提示
   * @param {string} message 提示信息
   * @param {number} duration 持续时间，默认2000ms
   */
  showError(message, duration = 2000) {
    if (Toast && Toast.fail) {
      Toast.fail({
        message,
        duration,
        forbidClick: true
      });
    } else {
      // 使用原生微信API
      wx.showToast({
        title: message,
        icon: 'error',
        duration
      });
    }
  }

  /**
   * 显示普通提示
   * @param {string} message 提示信息
   * @param {number} duration 持续时间，默认2000ms
   */
  showToast(message, duration = 2000) {
    if (Toast) {
      Toast({
        message,
        duration,
        forbidClick: true
      });
    } else {
      // 使用原生微信API
      wx.showToast({
        title: message,
        icon: 'none',
        duration
      });
    }
  }

  /**
   * 显示加载中
   * @param {string} message 加载提示文字，默认"加载中..."
   */
  showLoading(message = '加载中...') {
    this.loadingCount++;
    if (Toast && Toast.loading) {
      Toast.loading({
        message,
        forbidClick: true,
        duration: 0 // 不自动关闭
      });
    } else {
      // 使用原生微信API
      wx.showLoading({
        title: message
      });
    }
  }

  /**
   * 隐藏加载中
   */
  hideLoading() {
    this.loadingCount--;
    if (this.loadingCount <= 0) {
      this.loadingCount = 0;
      if (Toast && Toast.clear) {
        Toast.clear();
      } else {
        // 使用原生微信API
        wx.hideLoading();
      }
    }
  }

  /**
   * 显示确认对话框
   * @param {Object} options 配置选项
   * @returns {Promise}
   */
  showConfirm(options = {}) {
    const defaultOptions = {
      title: '提示',
      message: '确定要执行此操作吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      confirmButtonColor: '#3b82f6',
      cancelButtonColor: '#6b7280'
    };

    const finalOptions = {...defaultOptions, ...options};

    if (Dialog && Dialog.confirm) {
      return Dialog.confirm(finalOptions);
    } else {
      // 使用原生微信API
      return new Promise(resolve => {
        wx.showModal({
          title: finalOptions.title,
          content: finalOptions.message,
          confirmText: finalOptions.confirmButtonText,
          cancelText: finalOptions.cancelButtonText,
          success: res => resolve(res.confirm),
          fail: () => resolve(false)
        });
      });
    }
  }

  /**
   * 显示警告对话框
   * @param {Object} options 配置选项
   * @returns {Promise}
   */
  showAlert(options = {}) {
    const defaultOptions = {
      title: '提示',
      confirmButtonText: '确定',
      confirmButtonColor: '#3b82f6'
    };

    const finalOptions = {...defaultOptions, ...options};

    if (Dialog && Dialog.alert) {
      return Dialog.alert(finalOptions);
    } else {
      // 使用原生微信API
      return new Promise(resolve => {
        wx.showModal({
          title: finalOptions.title,
          content: finalOptions.message || '',
          confirmText: finalOptions.confirmButtonText,
          showCancel: false,
          success: () => resolve(true),
          fail: () => resolve(false)
        });
      });
    }
  }

  /**
   * 显示顶部通知
   * @param {Object} options 配置选项
   */
  showNotify(options = {}) {
    const defaultOptions = {
      type: 'primary',
      duration: 3000,
      safeAreaInsetTop: true
    };

    const finalOptions = {...defaultOptions, ...options};

    if (Notify) {
      Notify(finalOptions);
    } else {
      // 使用Toast作为替代
      this.showToast(finalOptions.message || '通知', finalOptions.duration);
    }
  }

  /**
   * 显示成功通知
   * @param {string} message 通知信息
   */
  showSuccessNotify(message) {
    this.showNotify({
      type: 'success',
      message
    });
  }

  /**
   * 显示错误通知
   * @param {string} message 通知信息
   */
  showErrorNotify(message) {
    this.showNotify({
      type: 'danger',
      message
    });
  }

  /**
   * 显示警告通知
   * @param {string} message 通知信息
   */
  showWarningNotify(message) {
    this.showNotify({
      type: 'warning',
      message
    });
  }

  /**
   * 清除所有提示
   */
  clearAll() {
    if (Toast && Toast.clear) {
      Toast.clear();
    } else {
      wx.hideLoading();
      wx.hideToast();
    }

    if (Dialog && Dialog.close) {
      Dialog.close();
    }

    if (Notify && Notify.clear) {
      Notify.clear();
    }

    this.loadingCount = 0;
  }

  /**
   * 网络请求包装器
   * @param {Function} requestFn 请求函数
   * @param {Object} options 配置选项
   */
  async withLoading(requestFn, options = {}) {
    const {
      loadingText = '加载中...',
      successText,
      errorText = '操作失败',
      showSuccess = false,
      showError = true
    } = options;

    try {
      this.showLoading(loadingText);
      const result = await requestFn();

      if (showSuccess && successText) {
        this.showSuccess(successText);
      }

      return result;
    } catch (error) {
      console.error('Request error:', error);

      if (showError) {
        const message = error.message || errorText;
        this.showError(message);
      }

      throw error;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * 防抖执行函数
   * @param {Function} fn 要执行的函数
   * @param {number} delay 延迟时间，默认300ms
   */
  debounce(fn, delay = 300) {
    let timer = null;
    return function (...args) {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        fn.apply(this, args);
      }, delay);
    };
  }

  /**
   * 节流执行函数
   * @param {Function} fn 要执行的函数
   * @param {number} delay 延迟时间，默认300ms
   */
  throttle(fn, delay = 300) {
    let timer = null;
    return function (...args) {
      if (timer) return;
      timer = setTimeout(() => {
        fn.apply(this, args);
        timer = null;
      }, delay);
    };
  }
}

// 创建全局实例
const ui = new UIManager();

// 挂载到全局 app
if (typeof getApp === 'function') {
  const app = getApp();
  if (app) {
    app.ui = ui;
  }
}

module.exports = ui;
